/* 量子共鸣者 - UI组件样式 */

/* UI组件基础变量 */
:root {
    /* UI特定颜色 */
    --ui-primary: #00ffff;
    --ui-secondary: #0080ff;
    --ui-accent: #ff00ff;
    --ui-background: rgba(10, 10, 15, 0.9);
    --ui-surface: rgba(26, 26, 46, 0.8);
    --ui-border: rgba(0, 255, 255, 0.3);
    
    /* UI尺寸 */
    --ui-scale: 1.0;
    --button-size: 52px;
    --font-size-base: 18px;
    
    /* 量子效果变量 */
    --quantum-glow-intensity: 1.0;
    --quantum-pulse-speed: 2s;
    --quantum-wave-amplitude: 0.5;
}

/* 设备响应式变量 */
.device-mobile {
    --ui-scale: 0.8;
    --button-size: 44px;
    --font-size-base: 14px;
}

.device-tablet {
    --ui-scale: 0.9;
    --button-size: 48px;
    --font-size-base: 16px;
}

/* 量子按钮样式 */
.quantum-button {
    position: relative;
    padding: 12px 24px;
    background: linear-gradient(45deg, var(--ui-primary), var(--ui-secondary));
    border: 2px solid var(--ui-border);
    border-radius: 8px;
    color: #000;
    font-weight: bold;
    font-size: calc(var(--font-size-base) * 0.9);
    cursor: pointer;
    overflow: hidden;
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    text-transform: uppercase;
    letter-spacing: 1px;
    min-width: var(--button-size);
    min-height: var(--button-size);
    display: flex;
    align-items: center;
    justify-content: center;
    user-select: none;
}

.quantum-button:hover {
    transform: translateY(-2px) scale(1.02);
    box-shadow: 0 8px 25px rgba(0, 255, 255, 0.4);
    border-color: var(--ui-primary);
}

.quantum-button:active {
    transform: translateY(0) scale(0.98);
}

.quantum-button:disabled {
    opacity: 0.5;
    cursor: not-allowed;
    transform: none;
}

/* 量子滑块样式 */
.quantum-slider {
    -webkit-appearance: none;
    appearance: none;
    width: 100%;
    height: 6px;
    background: rgba(255, 255, 255, 0.1);
    border-radius: 3px;
    outline: none;
    cursor: pointer;
}

.quantum-slider::-webkit-slider-thumb {
    -webkit-appearance: none;
    appearance: none;
    width: 20px;
    height: 20px;
    background: linear-gradient(45deg, var(--ui-primary), var(--ui-secondary));
    border-radius: 50%;
    cursor: pointer;
    box-shadow: 0 0 10px rgba(0, 255, 255, 0.5);
    transition: all 0.2s ease;
}

.quantum-slider::-webkit-slider-thumb:hover {
    transform: scale(1.2);
    box-shadow: 0 0 15px rgba(0, 255, 255, 0.8);
}

.quantum-slider::-moz-range-thumb {
    width: 20px;
    height: 20px;
    background: linear-gradient(45deg, var(--ui-primary), var(--ui-secondary));
    border-radius: 50%;
    cursor: pointer;
    border: none;
    box-shadow: 0 0 10px rgba(0, 255, 255, 0.5);
}

.slider-container {
    display: flex;
    align-items: center;
    gap: 15px;
}

.slider-value {
    min-width: 50px;
    text-align: right;
    color: var(--ui-primary);
    font-weight: bold;
}

/* 量子复选框样式 */
.quantum-checkbox {
    -webkit-appearance: none;
    appearance: none;
    width: 20px;
    height: 20px;
    border: 2px solid var(--ui-border);
    border-radius: 4px;
    background: transparent;
    cursor: pointer;
    position: relative;
    transition: all 0.3s ease;
}

.quantum-checkbox:checked {
    background: linear-gradient(45deg, var(--ui-primary), var(--ui-secondary));
    border-color: var(--ui-primary);
    box-shadow: 0 0 10px rgba(0, 255, 255, 0.5);
}

.quantum-checkbox:checked::after {
    content: '✓';
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    color: #000;
    font-weight: bold;
    font-size: 14px;
}

/* 量子选择框样式 */
.quantum-select {
    background: var(--ui-surface);
    border: 2px solid var(--ui-border);
    border-radius: 6px;
    color: var(--text-primary);
    padding: 8px 12px;
    font-size: var(--font-size-base);
    cursor: pointer;
    transition: all 0.3s ease;
    min-width: 120px;
}

.quantum-select:hover,
.quantum-select:focus {
    border-color: var(--ui-primary);
    box-shadow: 0 0 10px rgba(0, 255, 255, 0.3);
    outline: none;
}

.quantum-select option {
    background: var(--ui-surface);
    color: var(--text-primary);
}

/* HUD样式 */
#gameHUD {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    pointer-events: none;
    z-index: 100;
    display: flex;
    flex-direction: column;
    padding: 20px;
    gap: 15px;
}

.hud-score-container,
.hud-energy-container,
.hud-frequency-container,
.hud-combo-container,
.hud-time-container,
.hud-particle-container,
.hud-level-container {
    background: var(--ui-background);
    border: 1px solid var(--ui-border);
    border-radius: 8px;
    padding: 10px 15px;
    backdrop-filter: blur(10px);
    pointer-events: auto;
    position: absolute;
}

.hud-score-container {
    top: 20px;
    left: 20px;
}

.hud-energy-container {
    top: 20px;
    right: 20px;
    min-width: 200px;
}

.hud-frequency-container {
    top: 100px;
    right: 20px;
}

.hud-combo-container {
    top: 180px;
    right: 20px;
}

.hud-time-container {
    bottom: 20px;
    left: 20px;
}

.hud-particle-container {
    bottom: 20px;
    left: 150px;
}

.hud-level-container {
    top: 20px;
    left: 50%;
    transform: translateX(-50%);
}

.hud-controls {
    position: absolute;
    top: 20px;
    right: 250px;
    display: flex;
    gap: 10px;
}

.hud-label {
    font-size: 12px;
    color: var(--text-secondary);
    margin-bottom: 5px;
    text-transform: uppercase;
    letter-spacing: 1px;
}

.hud-score {
    font-size: 24px;
    font-weight: bold;
    color: var(--ui-primary);
    text-shadow: 0 0 10px rgba(0, 255, 255, 0.5);
}

.hud-time,
.hud-particle {
    font-size: 18px;
    font-weight: bold;
    color: var(--text-primary);
}

.hud-frequency {
    display: flex;
    align-items: center;
    gap: 10px;
}

.frequency-value {
    font-size: 20px;
    font-weight: bold;
    color: var(--ui-secondary);
}

.frequency-unit {
    font-size: 14px;
    color: var(--text-secondary);
}

.frequency-wave {
    width: 30px;
    height: 4px;
    background: linear-gradient(90deg, var(--ui-primary), var(--ui-secondary));
    border-radius: 2px;
    transition: height 0.1s ease;
}

.energy-bar-wrapper {
    display: flex;
    align-items: center;
    gap: 10px;
}

.energy-bar {
    position: relative;
    width: 150px;
    height: 8px;
    background: rgba(255, 255, 255, 0.1);
    border-radius: 4px;
    overflow: hidden;
}

.energy-fill {
    height: 100%;
    background: linear-gradient(90deg, #00ff00, #00ffff);
    border-radius: 4px;
    transition: width 0.3s ease;
}

.energy-glow {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(90deg, transparent, rgba(0, 255, 255, 0.5), transparent);
    border-radius: 4px;
    opacity: 0;
    transition: opacity 0.3s ease;
}

.energy-value {
    font-size: 14px;
    font-weight: bold;
    color: var(--text-primary);
    min-width: 40px;
}

.combo-wrapper {
    display: flex;
    align-items: center;
    gap: 10px;
}

.hud-combo {
    font-size: 22px;
    font-weight: bold;
    color: var(--ui-accent);
}

.combo-multiplier {
    font-size: 16px;
    color: var(--text-secondary);
}

.hud-level {
    text-align: center;
}

.level-number {
    font-size: 16px;
    font-weight: bold;
    color: var(--ui-primary);
}

.level-name {
    font-size: 14px;
    color: var(--text-secondary);
    margin-top: 2px;
}

.hud-button {
    width: 40px;
    height: 40px;
    background: var(--ui-surface);
    border: 1px solid var(--ui-border);
    border-radius: 6px;
    color: var(--text-primary);
    cursor: pointer;
    display: flex;
    align-items: center;
    justify-content: center;
    transition: all 0.3s ease;
    font-size: 18px;
}

.hud-button:hover {
    background: var(--ui-primary);
    color: #000;
    box-shadow: 0 0 10px rgba(0, 255, 255, 0.5);
}

/* 设置面板样式 */
.settings-panel {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(0, 0, 0, 0.8);
    backdrop-filter: blur(10px);
    z-index: 1000;
    display: none;
    align-items: center;
    justify-content: center;
}

.settings-panel .settings-container {
    background: var(--ui-background);
    border: 2px solid var(--ui-border);
    border-radius: 12px;
    width: 90%;
    max-width: 800px;
    max-height: 90%;
    display: flex;
    flex-direction: column;
    overflow: hidden;
}

.settings-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 20px;
    border-bottom: 1px solid var(--ui-border);
}

.settings-header h2 {
    color: var(--ui-primary);
    margin: 0;
}

.settings-close {
    background: none;
    border: none;
    color: var(--text-primary);
    font-size: 24px;
    cursor: pointer;
    width: 30px;
    height: 30px;
    display: flex;
    align-items: center;
    justify-content: center;
    border-radius: 4px;
    transition: all 0.3s ease;
}

.settings-close:hover {
    background: var(--error-color);
    color: #fff;
}

.settings-tabs {
    display: flex;
    border-bottom: 1px solid var(--ui-border);
    overflow-x: auto;
}

.settings-tab {
    padding: 15px 20px;
    background: none;
    border: none;
    color: var(--text-secondary);
    cursor: pointer;
    transition: all 0.3s ease;
    white-space: nowrap;
    border-bottom: 2px solid transparent;
}

.settings-tab:hover {
    color: var(--text-primary);
    background: rgba(255, 255, 255, 0.05);
}

.settings-tab.active {
    color: var(--ui-primary);
    border-bottom-color: var(--ui-primary);
}

.settings-content {
    flex: 1;
    overflow-y: auto;
    padding: 20px;
}

.settings-panel[data-panel] {
    display: none;
}

.settings-panel.active {
    display: block;
}

.settings-group {
    margin-bottom: 30px;
}

.settings-group h3 {
    color: var(--ui-secondary);
    margin-bottom: 15px;
    font-size: 18px;
}

.settings-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 12px 0;
    border-bottom: 1px solid rgba(255, 255, 255, 0.1);
}

.settings-item:last-child {
    border-bottom: none;
}

.settings-item label {
    color: var(--text-primary);
    font-size: 16px;
    flex: 1;
}

.settings-description {
    color: var(--text-secondary);
    font-size: 14px;
    line-height: 1.5;
    margin: 0;
}

.settings-footer {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 20px;
    border-top: 1px solid var(--ui-border);
}

.settings-actions {
    display: flex;
    gap: 10px;
}

.settings-button {
    padding: 10px 20px;
    border: 2px solid var(--ui-border);
    border-radius: 6px;
    cursor: pointer;
    font-size: 14px;
    font-weight: bold;
    transition: all 0.3s ease;
    text-transform: uppercase;
}

.settings-button.primary {
    background: linear-gradient(45deg, var(--ui-primary), var(--ui-secondary));
    color: #000;
    border-color: var(--ui-primary);
}

.settings-button.primary:hover {
    box-shadow: 0 0 15px rgba(0, 255, 255, 0.5);
}

.settings-button.secondary {
    background: transparent;
    color: var(--text-primary);
}

.settings-button.secondary:hover {
    background: var(--ui-surface);
    border-color: var(--ui-primary);
}

/* 设置面板动画 */
.settings-enter {
    animation: settingsEnter 0.3s cubic-bezier(0.4, 0, 0.2, 1) forwards;
}

.settings-exit {
    animation: settingsExit 0.3s cubic-bezier(0.4, 0, 1, 1) forwards;
}

@keyframes settingsEnter {
    from {
        opacity: 0;
        transform: scale(0.9);
    }
    to {
        opacity: 1;
        transform: scale(1);
    }
}

@keyframes settingsExit {
    from {
        opacity: 1;
        transform: scale(1);
    }
    to {
        opacity: 0;
        transform: scale(0.9);
    }
}

/* 设置消息样式 */
.settings-message {
    position: absolute;
    top: 20px;
    right: 20px;
    padding: 10px 15px;
    border-radius: 6px;
    color: #fff;
    font-size: 14px;
    z-index: 1001;
    animation: messageSlideIn 0.3s ease-out;
}

.settings-message.success {
    background: var(--success-color);
}

.settings-message.error {
    background: var(--error-color);
}

.settings-message.info {
    background: var(--info-color);
}

@keyframes messageSlideIn {
    from {
        transform: translateX(100%);
        opacity: 0;
    }
    to {
        transform: translateX(0);
        opacity: 1;
    }
}

/* 辅助功能样式 */
.high-contrast {
    --ui-primary: #ffffff;
    --ui-secondary: #ffff00;
    --ui-background: #000000;
    --ui-surface: #333333;
    --text-primary: #ffffff;
    --text-secondary: #cccccc;
}

.large-text {
    --font-size-base: 24px;
}

.reduce-motion * {
    animation-duration: 0.01ms !important;
    animation-iteration-count: 1 !important;
    transition-duration: 0.01ms !important;
}

/* 色盲模式 */
.colorblind-protanopia {
    filter: url(#protanopia-filter);
}

.colorblind-deuteranopia {
    filter: url(#deuteranopia-filter);
}

.colorblind-tritanopia {
    filter: url(#tritanopia-filter);
}

/* 关卡选择界面样式 */
.level-select-screen {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: var(--bg-gradient);
    display: none;
    flex-direction: column;
    z-index: 50;
}

.level-select-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 20px;
    border-bottom: 1px solid var(--ui-border);
    background: var(--ui-background);
}

.level-select-header h2 {
    color: var(--ui-primary);
    margin: 0;
    font-size: 28px;
}

.level-back-button {
    background: var(--ui-surface);
    border: 2px solid var(--ui-border);
    border-radius: 6px;
    color: var(--text-primary);
    padding: 10px 20px;
    cursor: pointer;
    transition: all 0.3s ease;
}

.level-back-button:hover {
    border-color: var(--ui-primary);
    box-shadow: 0 0 10px rgba(0, 255, 255, 0.3);
}

.level-select-content {
    flex: 1;
    display: flex;
    gap: 20px;
    padding: 20px;
    overflow: hidden;
}

.level-grid {
    flex: 2;
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(280px, 1fr));
    gap: 20px;
    overflow-y: auto;
    padding-right: 10px;
}

.level-card {
    background: var(--ui-surface);
    border: 2px solid var(--ui-border);
    border-radius: 12px;
    padding: 20px;
    cursor: pointer;
    transition: all 0.3s ease;
    position: relative;
    min-height: 180px;
    display: flex;
    flex-direction: column;
}

.level-card:hover {
    border-color: var(--ui-primary);
    box-shadow: 0 5px 20px rgba(0, 255, 255, 0.2);
    transform: translateY(-2px);
}

.level-card.selected {
    border-color: var(--ui-primary);
    box-shadow: 0 0 20px rgba(0, 255, 255, 0.4);
}

.level-card.locked {
    opacity: 0.6;
    cursor: not-allowed;
}

.level-card.locked:hover {
    transform: none;
    box-shadow: none;
    border-color: var(--ui-border);
}

.level-card-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 15px;
}

.level-number {
    width: 40px;
    height: 40px;
    background: linear-gradient(45deg, var(--ui-primary), var(--ui-secondary));
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-weight: bold;
    color: #000;
    font-size: 18px;
}

.level-stars {
    display: flex;
    gap: 2px;
}

.level-stars .star {
    color: #ffd700;
    font-size: 16px;
}

.level-stars .star:not(.filled) {
    color: rgba(255, 215, 0, 0.3);
}

.level-card-content {
    flex: 1;
    display: flex;
    flex-direction: column;
}

.level-name {
    color: var(--text-primary);
    margin: 0 0 10px 0;
    font-size: 20px;
}

.level-description {
    color: var(--text-secondary);
    margin: 0 0 15px 0;
    line-height: 1.4;
    flex: 1;
}

.level-info {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-top: auto;
}

.level-best-score {
    color: var(--ui-secondary);
    font-size: 14px;
    font-weight: bold;
}

.level-difficulty-badges {
    display: flex;
    gap: 5px;
}

.difficulty-badge {
    padding: 2px 8px;
    border-radius: 12px;
    font-size: 12px;
    font-weight: bold;
    text-transform: uppercase;
}

.difficulty-badge.easy {
    background: rgba(0, 255, 0, 0.2);
    color: #00ff00;
}

.difficulty-badge.normal {
    background: rgba(255, 255, 0, 0.2);
    color: #ffff00;
}

.difficulty-badge.hard {
    background: rgba(255, 165, 0, 0.2);
    color: #ffa500;
}

.difficulty-badge.expert {
    background: rgba(255, 0, 0, 0.2);
    color: #ff0000;
}

.level-lock-overlay {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(0, 0, 0, 0.7);
    border-radius: 12px;
    display: flex;
    align-items: center;
    justify-content: center;
}

.lock-icon {
    font-size: 48px;
    opacity: 0.8;
}

.level-preview {
    flex: 1;
    background: var(--ui-surface);
    border: 2px solid var(--ui-border);
    border-radius: 12px;
    padding: 20px;
    display: flex;
    flex-direction: column;
    gap: 20px;
    max-width: 400px;
    max-height: calc(100vh - 140px); /* 限制最大高度，预留头部和边距空间 */
    overflow-y: auto; /* 添加垂直滚动 */
}

.preview-header {
    text-align: center;
}

.preview-header h3 {
    color: var(--ui-primary);
    margin: 0 0 10px 0;
    font-size: 24px;
}

.preview-stars {
    display: flex;
    justify-content: center;
    gap: 5px;
}

.preview-stars .star {
    color: #ffd700;
    font-size: 20px;
}

.preview-stars .star:not(.filled) {
    color: rgba(255, 215, 0, 0.3);
}

.preview-description {
    color: var(--text-secondary);
    text-align: center;
    line-height: 1.5;
}

.preview-canvas-container {
    display: flex;
    justify-content: center;
    background: rgba(0, 0, 0, 0.3);
    border-radius: 8px;
    padding: 10px;
}

#levelPreviewCanvas {
    border-radius: 6px;
    border: 1px solid var(--ui-border);
}

.preview-stats {
    display: grid;
    grid-template-columns: 1fr 1fr 1fr;
    gap: 15px;
}

.stat-item {
    text-align: center;
}

.stat-label {
    display: block;
    color: var(--text-secondary);
    font-size: 12px;
    margin-bottom: 5px;
    text-transform: uppercase;
}

.stat-value {
    display: block;
    color: var(--ui-primary);
    font-size: 18px;
    font-weight: bold;
}

.difficulty-selector {
    /* 确保难度选择器在滚动区域内正常显示 */
    flex-shrink: 0; /* 防止被压缩 */
    min-height: auto; /* 允许自适应高度 */
}

.difficulty-selector h4 {
    color: var(--text-primary);
    margin: 0 0 10px 0;
    text-align: center;
}

.difficulty-buttons {
    display: flex;
    gap: 10px;
    justify-content: center;
    flex-wrap: wrap;
}

.difficulty-button {
    padding: 8px 16px;
    border: 2px solid var(--ui-border);
    border-radius: 6px;
    background: transparent;
    color: var(--text-primary);
    cursor: pointer;
    transition: all 0.3s ease;
    font-size: 14px;
    font-weight: bold;
    text-transform: uppercase;
}

.difficulty-button:hover {
    border-color: var(--ui-primary);
}

.difficulty-button.selected {
    background: linear-gradient(45deg, var(--ui-primary), var(--ui-secondary));
    color: #000;
    border-color: var(--ui-primary);
}

.difficulty-button.easy.selected {
    background: linear-gradient(45deg, #00ff00, #00cc00);
}

.difficulty-button.normal.selected {
    background: linear-gradient(45deg, #ffff00, #cccc00);
}

.difficulty-button.hard.selected {
    background: linear-gradient(45deg, #ffa500, #cc8400);
}

.difficulty-button.expert.selected {
    background: linear-gradient(45deg, #ff0000, #cc0000);
}

.preview-actions {
    text-align: center;
    flex-shrink: 0; /* 防止开始游戏按钮被压缩 */
    margin-top: auto; /* 将按钮推到底部 */
}

#startLevelButton {
    min-width: 150px;
    padding: 15px 30px;
    font-size: 16px;
}

#startLevelButton:disabled {
    opacity: 0.5;
    cursor: not-allowed;
    transform: none;
}

/* 关卡卡片进入动画 */
.level-card {
    animation: cardEnter 0.6s cubic-bezier(0.175, 0.885, 0.32, 1.275) forwards;
    opacity: 0;
    transform: translateY(30px) scale(0.9);
}

.level-card:nth-child(1) { animation-delay: 0.1s; }
.level-card:nth-child(2) { animation-delay: 0.2s; }
.level-card:nth-child(3) { animation-delay: 0.3s; }
.level-card:nth-child(4) { animation-delay: 0.4s; }
.level-card:nth-child(5) { animation-delay: 0.5s; }
.level-card:nth-child(6) { animation-delay: 0.6s; }

@keyframes cardEnter {
    to {
        opacity: 1;
        transform: translateY(0) scale(1);
    }
}

/* 关卡编辑器样式 */
.level-editor-screen {
    display: none;
    flex-direction: column;
    height: 100vh;
    background: var(--bg-primary);
    color: var(--text-primary);
}

.editor-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 1rem 2rem;
    background: var(--bg-secondary);
    border-bottom: 2px solid var(--ui-border);
}

.editor-header h2 {
    margin: 0;
    color: var(--accent-primary);
    font-size: 1.5rem;
}

.editor-actions {
    display: flex;
    gap: 0.5rem;
}

.editor-button {
    padding: 0.5rem 1rem;
    background: var(--accent-primary);
    color: var(--text-primary);
    border: none;
    border-radius: 4px;
    cursor: pointer;
    font-size: 0.9rem;
    transition: all 0.3s ease;
}

.editor-button:hover {
    background: var(--accent-secondary);
    transform: translateY(-2px);
}

.editor-button.secondary {
    background: var(--ui-border);
}

.editor-button.secondary:hover {
    background: var(--text-secondary);
}

.editor-content {
    display: flex;
    flex: 1;
    overflow: hidden;
}

.editor-sidebar {
    width: 250px;
    background: var(--bg-secondary);
    border-right: 2px solid var(--ui-border);
    display: flex;
    flex-direction: column;
}

.editor-toolbar {
    padding: 1rem;
    border-bottom: 1px solid var(--ui-border);
}

.editor-toolbar h3 {
    margin: 0 0 1rem 0;
    color: var(--accent-primary);
    font-size: 1.1rem;
}

.tool-group {
    display: flex;
    flex-direction: column;
    gap: 0.5rem;
    margin-bottom: 1rem;
}

.tool-button {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    padding: 0.75rem;
    background: var(--bg-primary);
    border: 2px solid var(--ui-border);
    border-radius: 6px;
    cursor: pointer;
    transition: all 0.3s ease;
    color: var(--text-primary);
}

.tool-button:hover {
    border-color: var(--accent-primary);
    background: var(--bg-hover);
}

.tool-button.active {
    border-color: var(--accent-primary);
    background: var(--accent-primary);
    color: var(--text-primary);
}

.tool-icon {
    font-size: 1.2rem;
}

.tool-name {
    font-size: 0.9rem;
}

.editor-options h4 {
    margin: 0 0 0.5rem 0;
    color: var(--text-secondary);
    font-size: 0.9rem;
}

.option-item {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    margin-bottom: 0.5rem;
    cursor: pointer;
    color: var(--text-primary);
    font-size: 0.9rem;
}

.option-item input[type="checkbox"] {
    accent-color: var(--accent-primary);
}

.editor-properties {
    padding: 1rem;
    flex: 1;
    overflow-y: auto;
}

.editor-properties h3 {
    margin: 0 0 1rem 0;
    color: var(--accent-primary);
    font-size: 1.1rem;
}

.properties-content {
    display: flex;
    flex-direction: column;
    gap: 1rem;
}

.property-group {
    display: flex;
    flex-direction: column;
    gap: 0.5rem;
}

.property-group label {
    color: var(--text-secondary);
    font-size: 0.9rem;
}

.property-group input {
    padding: 0.5rem;
    background: var(--bg-primary);
    border: 1px solid var(--ui-border);
    border-radius: 4px;
    color: var(--text-primary);
    font-size: 0.9rem;
}

.property-group input:focus {
    outline: none;
    border-color: var(--accent-primary);
}

.no-selection {
    color: var(--text-secondary);
    font-style: italic;
    text-align: center;
    margin: 2rem 0;
}

.editor-main {
    flex: 1;
    display: flex;
    flex-direction: column;
    background: var(--bg-primary);
}

.editor-canvas-container {
    flex: 1;
    display: flex;
    justify-content: center;
    align-items: center;
    padding: 2rem;
}

.editor-status {
    display: flex;
    gap: 2rem;
    padding: 1rem 2rem;
    background: var(--bg-secondary);
    border-top: 1px solid var(--ui-border);
    font-size: 0.9rem;
}

.status-item {
    color: var(--text-secondary);
}

.status-item span {
    color: var(--text-primary);
    font-weight: bold;
}

.editor-panel {
    width: 300px;
    background: var(--bg-secondary);
    border-left: 2px solid var(--ui-border);
    display: flex;
    flex-direction: column;
    overflow-y: auto;
}

.level-info-panel,
.difficulty-panel,
.objectives-panel {
    padding: 1rem;
    border-bottom: 1px solid var(--ui-border);
}

.level-info-panel h3,
.difficulty-panel h3,
.objectives-panel h3 {
    margin: 0 0 1rem 0;
    color: var(--accent-primary);
    font-size: 1.1rem;
}

.info-group,
.setting-group {
    display: flex;
    flex-direction: column;
    gap: 0.5rem;
    margin-bottom: 1rem;
}

.info-group label,
.setting-group label {
    color: var(--text-secondary);
    font-size: 0.9rem;
}

.info-input,
.info-textarea,
.info-select,
.setting-input {
    padding: 0.5rem;
    background: var(--bg-primary);
    border: 1px solid var(--ui-border);
    border-radius: 4px;
    color: var(--text-primary);
    font-size: 0.9rem;
}

.info-textarea {
    resize: vertical;
    min-height: 60px;
}

.info-input:focus,
.info-textarea:focus,
.info-select:focus,
.setting-input:focus {
    outline: none;
    border-color: var(--accent-primary);
}

.difficulty-tabs {
    display: flex;
    gap: 0.5rem;
    margin-bottom: 1rem;
}

.difficulty-tab {
    flex: 1;
    padding: 0.5rem;
    background: var(--bg-primary);
    border: 1px solid var(--ui-border);
    border-radius: 4px;
    cursor: pointer;
    font-size: 0.9rem;
    color: var(--text-primary);
    transition: all 0.3s ease;
}

.difficulty-tab:hover {
    border-color: var(--accent-primary);
}

.difficulty-tab.active {
    background: var(--accent-primary);
    border-color: var(--accent-primary);
}

.objectives-list {
    display: flex;
    flex-direction: column;
    gap: 0.5rem;
    margin-bottom: 1rem;
    max-height: 200px;
    overflow-y: auto;
}

.objective-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 0.5rem;
    background: var(--bg-primary);
    border: 1px solid var(--ui-border);
    border-radius: 4px;
    font-size: 0.9rem;
}

.objective-remove {
    background: var(--error-color);
    color: white;
    border: none;
    border-radius: 3px;
    padding: 0.25rem 0.5rem;
    cursor: pointer;
    font-size: 0.8rem;
}

.add-objective-btn {
    width: 100%;
    padding: 0.75rem;
    background: var(--accent-primary);
    color: var(--text-primary);
    border: none;
    border-radius: 4px;
    cursor: pointer;
    font-size: 0.9rem;
    transition: all 0.3s ease;
}

.add-objective-btn:hover {
    background: var(--accent-secondary);
}

/* 游戏结束屏幕样式 */
.game-over-screen {
    display: none;
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    z-index: 1000;
}

.game-over-overlay {
    position: relative;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.9);
    display: flex;
    justify-content: center;
    align-items: center;
}

.game-over-content {
    background: var(--bg-secondary);
    border: 2px solid var(--ui-border);
    border-radius: 12px;
    padding: 2rem;
    max-width: 800px;
    max-height: 90vh;
    overflow-y: auto;
    box-shadow: 0 20px 40px rgba(0, 0, 0, 0.5);
    animation: gameOverSlideIn 0.5s ease-out;
}

@keyframes gameOverSlideIn {
    from {
        opacity: 0;
        transform: translateY(-50px) scale(0.9);
    }
    to {
        opacity: 1;
        transform: translateY(0) scale(1);
    }
}

.game-over-header {
    text-align: center;
    margin-bottom: 2rem;
    border-bottom: 2px solid var(--ui-border);
    padding-bottom: 1rem;
}

.game-over-title {
    font-size: 2.5rem;
    margin: 0 0 1rem 0;
    background: linear-gradient(45deg, var(--accent-primary), var(--accent-secondary));
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
    text-shadow: 0 0 20px var(--accent-primary);
}

.game-over-title.success {
    background: linear-gradient(45deg, #00ff88, #00ccff);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}

.game-over-title.failed {
    background: linear-gradient(45deg, #ff4444, #ff8844);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}

.level-info {
    display: flex;
    justify-content: center;
    align-items: center;
    gap: 1rem;
}

.level-name {
    font-size: 1.2rem;
    color: var(--text-primary);
    font-weight: bold;
}

.difficulty-badge {
    padding: 0.25rem 0.75rem;
    border-radius: 20px;
    font-size: 0.9rem;
    font-weight: bold;
    text-transform: uppercase;
}

.difficulty-badge.easy {
    background: #4CAF50;
    color: white;
}

.difficulty-badge.normal {
    background: #2196F3;
    color: white;
}

.difficulty-badge.hard {
    background: #FF9800;
    color: white;
}

.difficulty-badge.expert {
    background: #F44336;
    color: white;
}

.game-over-main {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 2rem;
    margin-bottom: 2rem;
}

.score-section {
    text-align: center;
}

.final-score {
    margin-bottom: 2rem;
}

.score-label {
    display: block;
    font-size: 1.1rem;
    color: var(--text-secondary);
    margin-bottom: 0.5rem;
}

.score-value {
    display: block;
    font-size: 3rem;
    font-weight: bold;
    color: var(--accent-primary);
    text-shadow: 0 0 20px var(--accent-primary);
    font-family: 'Courier New', monospace;
}

.stars-display {
    display: flex;
    justify-content: center;
    gap: 1rem;
}

.star {
    font-size: 3rem;
    opacity: 0.3;
    transition: all 0.5s ease;
    transform: scale(0.8);
}

.star.earned {
    opacity: 1;
    transform: scale(1.2);
    filter: drop-shadow(0 0 10px #ffd700);
}

.star.animate {
    animation: starEarn 0.6s ease-out;
}

@keyframes starEarn {
    0% {
        transform: scale(0.8) rotate(0deg);
        opacity: 0.3;
    }
    50% {
        transform: scale(1.5) rotate(180deg);
        opacity: 1;
    }
    100% {
        transform: scale(1.2) rotate(360deg);
        opacity: 1;
    }
}

.stats-section {
    background: var(--bg-primary);
    border: 1px solid var(--ui-border);
    border-radius: 8px;
    padding: 1.5rem;
}

.stats-title {
    margin: 0 0 1rem 0;
    color: var(--accent-primary);
    font-size: 1.2rem;
    text-align: center;
}

.stats-grid {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 1rem;
}

.stat-item {
    display: flex;
    flex-direction: column;
    align-items: center;
    text-align: center;
}

.stat-label {
    font-size: 0.9rem;
    color: var(--text-secondary);
    margin-bottom: 0.25rem;
}

.stat-value {
    font-size: 1.2rem;
    font-weight: bold;
    color: var(--text-primary);
}

.achievements-section {
    grid-column: 1 / -1;
    background: var(--bg-primary);
    border: 1px solid var(--ui-border);
    border-radius: 8px;
    padding: 1.5rem;
    margin-top: 1rem;
}

.achievements-title {
    margin: 0 0 1rem 0;
    color: var(--accent-primary);
    font-size: 1.2rem;
    text-align: center;
}

.achievements-list {
    display: flex;
    flex-direction: column;
    gap: 0.75rem;
}

.achievement-item {
    display: flex;
    align-items: center;
    gap: 1rem;
    padding: 0.75rem;
    background: var(--bg-secondary);
    border: 1px solid var(--ui-border);
    border-radius: 6px;
    animation: achievementSlideIn 0.5s ease-out;
}

@keyframes achievementSlideIn {
    from {
        opacity: 0;
        transform: translateX(-20px);
    }
    to {
        opacity: 1;
        transform: translateX(0);
    }
}

.achievement-icon {
    font-size: 2rem;
    flex-shrink: 0;
}

.achievement-info {
    display: flex;
    flex-direction: column;
}

.achievement-name {
    font-weight: bold;
    color: var(--text-primary);
    margin-bottom: 0.25rem;
}

.achievement-description {
    font-size: 0.9rem;
    color: var(--text-secondary);
}

.no-achievements {
    text-align: center;
    color: var(--text-secondary);
    font-style: italic;
    margin: 1rem 0;
}

.game-over-footer {
    border-top: 2px solid var(--ui-border);
    padding-top: 2rem;
}

.action-buttons {
    display: flex;
    justify-content: center;
    gap: 1rem;
    margin-bottom: 1rem;
    flex-wrap: wrap;
}

.game-over-btn {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    padding: 0.75rem 1.5rem;
    border: none;
    border-radius: 6px;
    cursor: pointer;
    font-size: 1rem;
    transition: all 0.3s ease;
    text-decoration: none;
}

.game-over-btn.primary {
    background: var(--accent-primary);
    color: var(--text-primary);
}

.game-over-btn.primary:hover {
    background: var(--accent-secondary);
    transform: translateY(-2px);
}

.game-over-btn.secondary {
    background: var(--bg-primary);
    color: var(--text-primary);
    border: 2px solid var(--ui-border);
}

.game-over-btn.secondary:hover {
    border-color: var(--accent-primary);
    background: var(--bg-hover);
}

.btn-icon {
    font-size: 1.2rem;
}

.btn-text {
    font-weight: bold;
}

.social-actions {
    display: flex;
    justify-content: center;
    gap: 1rem;
}

.social-btn {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    padding: 0.5rem 1rem;
    background: var(--bg-primary);
    border: 1px solid var(--ui-border);
    border-radius: 4px;
    cursor: pointer;
    font-size: 0.9rem;
    color: var(--text-secondary);
    transition: all 0.3s ease;
}

.social-btn:hover {
    border-color: var(--accent-primary);
    color: var(--text-primary);
}

.game-over-background {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    pointer-events: none;
    z-index: -1;
}

/* 成就系统样式 */
.achievements-screen {
    display: none;
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    z-index: 1000;
}

.achievements-overlay {
    position: relative;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.9);
    display: flex;
    justify-content: center;
    align-items: center;
}

.achievements-content {
    background: var(--bg-secondary);
    border: 2px solid var(--ui-border);
    border-radius: 12px;
    padding: 2rem;
    max-width: 900px;
    max-height: 90vh;
    overflow-y: auto;
    box-shadow: 0 20px 40px rgba(0, 0, 0, 0.5);
    animation: achievementsSlideIn 0.5s ease-out;
}

@keyframes achievementsSlideIn {
    from {
        opacity: 0;
        transform: translateY(-50px) scale(0.9);
    }
    to {
        opacity: 1;
        transform: translateY(0) scale(1);
    }
}

.achievements-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 2rem;
    border-bottom: 2px solid var(--ui-border);
    padding-bottom: 1rem;
}

.achievements-title {
    font-size: 2rem;
    margin: 0;
    background: linear-gradient(45deg, var(--accent-primary), var(--accent-secondary));
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}

.close-btn {
    background: none;
    border: none;
    font-size: 1.5rem;
    color: var(--text-secondary);
    cursor: pointer;
    padding: 0.5rem;
    border-radius: 4px;
    transition: all 0.3s ease;
}

.close-btn:hover {
    background: var(--bg-hover);
    color: var(--text-primary);
}

.achievements-summary {
    display: grid;
    grid-template-columns: 2fr 1fr;
    gap: 2rem;
    margin-bottom: 2rem;
    padding: 1.5rem;
    background: var(--bg-primary);
    border: 1px solid var(--ui-border);
    border-radius: 8px;
}

.achievement-progress {
    display: flex;
    flex-direction: column;
    gap: 1rem;
}

.progress-info {
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.progress-text {
    font-size: 1.1rem;
    color: var(--text-primary);
    font-weight: bold;
}

.progress-count {
    font-size: 1.1rem;
    color: var(--accent-primary);
    font-weight: bold;
}

.progress-bar-container {
    width: 100%;
    height: 12px;
    background: var(--bg-secondary);
    border-radius: 6px;
    overflow: hidden;
    border: 1px solid var(--ui-border);
}

.progress-bar {
    width: 100%;
    height: 100%;
    position: relative;
}

.progress-fill {
    height: 100%;
    background: linear-gradient(90deg, var(--accent-primary), var(--accent-secondary));
    border-radius: 6px;
    transition: width 0.5s ease;
    box-shadow: 0 0 10px var(--accent-primary);
}

.achievement-stats {
    display: flex;
    flex-direction: column;
    gap: 1rem;
    justify-content: center;
}

.stat-item {
    text-align: center;
    padding: 1rem;
    background: var(--bg-secondary);
    border: 1px solid var(--ui-border);
    border-radius: 6px;
}

.stat-value {
    display: block;
    font-size: 2rem;
    font-weight: bold;
    color: var(--accent-primary);
    margin-bottom: 0.5rem;
}

.stat-label {
    font-size: 0.9rem;
    color: var(--text-secondary);
}

.achievements-filters {
    display: flex;
    gap: 0.5rem;
    margin-bottom: 2rem;
    flex-wrap: wrap;
}

.filter-btn {
    padding: 0.5rem 1rem;
    background: var(--bg-primary);
    border: 2px solid var(--ui-border);
    border-radius: 20px;
    cursor: pointer;
    font-size: 0.9rem;
    color: var(--text-secondary);
    transition: all 0.3s ease;
}

.filter-btn:hover {
    border-color: var(--accent-primary);
    color: var(--text-primary);
}

.filter-btn.active {
    background: var(--accent-primary);
    border-color: var(--accent-primary);
    color: var(--text-primary);
}

.achievements-main {
    max-height: 500px;
    overflow-y: auto;
}

.achievement-category {
    margin-bottom: 2rem;
}

.category-title {
    font-size: 1.3rem;
    color: var(--accent-primary);
    margin: 0 0 1rem 0;
    padding-bottom: 0.5rem;
    border-bottom: 1px solid var(--ui-border);
}

.category-achievements {
    display: flex;
    flex-direction: column;
    gap: 0.75rem;
}

.achievement-item {
    display: flex;
    align-items: center;
    gap: 1rem;
    padding: 1rem;
    background: var(--bg-primary);
    border: 1px solid var(--ui-border);
    border-radius: 8px;
    transition: all 0.3s ease;
}

.achievement-item:hover {
    border-color: var(--accent-primary);
    background: var(--bg-hover);
}

.achievement-item.unlocked {
    border-color: var(--accent-secondary);
    background: linear-gradient(135deg, var(--bg-primary), rgba(0, 255, 136, 0.1));
}

.achievement-item.locked {
    opacity: 0.6;
}

.achievement-icon {
    font-size: 2.5rem;
    flex-shrink: 0;
    width: 60px;
    height: 60px;
    display: flex;
    align-items: center;
    justify-content: center;
    background: var(--bg-secondary);
    border: 2px solid var(--ui-border);
    border-radius: 50%;
}

.achievement-item.unlocked .achievement-icon {
    border-color: var(--accent-secondary);
    background: linear-gradient(135deg, var(--bg-secondary), rgba(0, 255, 136, 0.2));
}

.achievement-info {
    flex: 1;
    display: flex;
    flex-direction: column;
    gap: 0.25rem;
}

.achievement-name {
    font-size: 1.1rem;
    font-weight: bold;
    color: var(--text-primary);
}

.achievement-description {
    font-size: 0.9rem;
    color: var(--text-secondary);
    line-height: 1.4;
}

.achievement-meta {
    display: flex;
    gap: 1rem;
    margin-top: 0.5rem;
}

.achievement-points {
    font-size: 0.8rem;
    color: var(--accent-primary);
    font-weight: bold;
}

.achievement-date {
    font-size: 0.8rem;
    color: var(--text-secondary);
}

.achievement-status {
    flex-shrink: 0;
    font-size: 1.5rem;
}

.status-unlocked {
    color: var(--accent-secondary);
}

.status-locked {
    color: var(--text-secondary);
}

/* 成就通知样式 */
.achievement-notification {
    position: fixed;
    top: 20px;
    right: 20px;
    background: var(--bg-secondary);
    border: 2px solid var(--accent-secondary);
    border-radius: 12px;
    padding: 1rem;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.5);
    z-index: 10000;
    transform: translateX(400px);
    opacity: 0;
    transition: all 0.5s ease;
    max-width: 350px;
}

.achievement-notification.show {
    transform: translateX(0);
    opacity: 1;
}

.notification-content {
    display: flex;
    align-items: center;
    gap: 1rem;
}

.notification-icon {
    font-size: 2rem;
    flex-shrink: 0;
}

.notification-info {
    flex: 1;
}

.notification-title {
    font-size: 0.9rem;
    color: var(--accent-secondary);
    font-weight: bold;
    margin-bottom: 0.25rem;
}

.notification-name {
    font-size: 1.1rem;
    color: var(--text-primary);
    font-weight: bold;
    margin-bottom: 0.25rem;
}

.notification-description {
    font-size: 0.8rem;
    color: var(--text-secondary);
    margin-bottom: 0.5rem;
}

.notification-points {
    font-size: 0.8rem;
    color: var(--accent-primary);
    font-weight: bold;
}

/* 排行榜样式 */
.leaderboard-screen {
    display: none;
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    z-index: 1000;
}

.leaderboard-overlay {
    position: relative;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.9);
    display: flex;
    justify-content: center;
    align-items: center;
}

.leaderboard-content {
    background: var(--bg-secondary);
    border: 2px solid var(--ui-border);
    border-radius: 12px;
    padding: 2rem;
    max-width: 800px;
    max-height: 90vh;
    overflow-y: auto;
    box-shadow: 0 20px 40px rgba(0, 0, 0, 0.5);
    animation: leaderboardSlideIn 0.5s ease-out;
}

@keyframes leaderboardSlideIn {
    from {
        opacity: 0;
        transform: translateY(-50px) scale(0.9);
    }
    to {
        opacity: 1;
        transform: translateY(0) scale(1);
    }
}

.leaderboard-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 2rem;
    border-bottom: 2px solid var(--ui-border);
    padding-bottom: 1rem;
}

.leaderboard-title {
    font-size: 2rem;
    margin: 0;
    background: linear-gradient(45deg, var(--accent-primary), var(--accent-secondary));
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}

.leaderboard-tabs {
    display: flex;
    gap: 0.5rem;
    margin-bottom: 2rem;
    flex-wrap: wrap;
}

.tab-btn {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    padding: 0.75rem 1rem;
    background: var(--bg-primary);
    border: 2px solid var(--ui-border);
    border-radius: 8px;
    cursor: pointer;
    font-size: 0.9rem;
    color: var(--text-secondary);
    transition: all 0.3s ease;
}

.tab-btn:hover {
    border-color: var(--accent-primary);
    color: var(--text-primary);
}

.tab-btn.active {
    background: var(--accent-primary);
    border-color: var(--accent-primary);
    color: var(--text-primary);
}

.tab-icon {
    font-size: 1.2rem;
}

.tab-text {
    font-weight: bold;
}

.player-rank-info {
    margin-bottom: 2rem;
}

.player-rank-card {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 1rem;
    background: linear-gradient(135deg, var(--bg-primary), rgba(0, 255, 136, 0.1));
    border: 2px solid var(--accent-secondary);
    border-radius: 8px;
}

.player-rank-card.no-rank {
    background: var(--bg-primary);
    border-color: var(--ui-border);
}

.rank-info {
    display: flex;
    flex-direction: column;
    align-items: flex-start;
}

.rank-position {
    font-size: 1.5rem;
    font-weight: bold;
    color: var(--accent-primary);
}

.rank-category {
    font-size: 0.9rem;
    color: var(--text-secondary);
}

.rank-details {
    display: flex;
    flex-direction: column;
    align-items: flex-end;
    text-align: right;
}

.player-name {
    font-size: 1.1rem;
    font-weight: bold;
    color: var(--text-primary);
}

.player-score {
    font-size: 1.2rem;
    color: var(--accent-secondary);
    font-weight: bold;
}

.rank-hint {
    font-size: 0.9rem;
    color: var(--text-secondary);
    font-style: italic;
}

.no-player {
    text-align: center;
    color: var(--text-secondary);
    font-style: italic;
    padding: 1rem;
}

.leaderboard-main {
    max-height: 400px;
    overflow-y: auto;
    margin-bottom: 2rem;
}

.leaderboard-list {
    display: flex;
    flex-direction: column;
    gap: 0.5rem;
}

.leaderboard-entry {
    display: flex;
    align-items: center;
    gap: 1rem;
    padding: 0.75rem;
    background: var(--bg-primary);
    border: 1px solid var(--ui-border);
    border-radius: 6px;
    transition: all 0.3s ease;
}

.leaderboard-entry:hover {
    border-color: var(--accent-primary);
    background: var(--bg-hover);
}

.leaderboard-entry.current-player {
    border-color: var(--accent-secondary);
    background: linear-gradient(135deg, var(--bg-primary), rgba(0, 255, 136, 0.1));
}

.leaderboard-entry.rank-first {
    border-color: #FFD700;
    background: linear-gradient(135deg, var(--bg-primary), rgba(255, 215, 0, 0.1));
}

.leaderboard-entry.rank-second {
    border-color: #C0C0C0;
    background: linear-gradient(135deg, var(--bg-primary), rgba(192, 192, 192, 0.1));
}

.leaderboard-entry.rank-third {
    border-color: #CD7F32;
    background: linear-gradient(135deg, var(--bg-primary), rgba(205, 127, 50, 0.1));
}

.leaderboard-entry.rank-top10 {
    border-color: var(--accent-primary);
}

.entry-rank {
    display: flex;
    flex-direction: column;
    align-items: center;
    min-width: 60px;
}

.rank-number {
    font-size: 1.2rem;
    font-weight: bold;
    color: var(--text-primary);
}

.rank-medal {
    font-size: 1.5rem;
    margin-top: 0.25rem;
}

.entry-player {
    flex: 1;
    display: flex;
    flex-direction: column;
}

.player-name {
    font-size: 1rem;
    font-weight: bold;
    color: var(--text-primary);
}

.entry-date {
    font-size: 0.8rem;
    color: var(--text-secondary);
}

.entry-score {
    display: flex;
    flex-direction: column;
    align-items: flex-end;
    text-align: right;
}

.score-value {
    font-size: 1.1rem;
    font-weight: bold;
    color: var(--accent-primary);
}

.level-name {
    font-size: 0.8rem;
    color: var(--text-secondary);
}

.empty-leaderboard {
    text-align: center;
    padding: 3rem 1rem;
    color: var(--text-secondary);
}

.empty-icon {
    font-size: 4rem;
    margin-bottom: 1rem;
}

.empty-text {
    font-size: 1.2rem;
    margin-bottom: 0.5rem;
}

.empty-hint {
    font-size: 0.9rem;
    font-style: italic;
}

.leaderboard-footer {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding-top: 1rem;
    border-top: 1px solid var(--ui-border);
}

.refresh-info {
    display: flex;
    flex-direction: column;
    font-size: 0.8rem;
    color: var(--text-secondary);
}

.refresh-text {
    margin-bottom: 0.25rem;
}

.refresh-time {
    font-weight: bold;
}

.refresh-btn {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    padding: 0.5rem 1rem;
    background: var(--accent-primary);
    border: none;
    border-radius: 6px;
    cursor: pointer;
    font-size: 0.9rem;
    color: var(--text-primary);
    transition: all 0.3s ease;
}

.refresh-btn:hover {
    background: var(--accent-secondary);
}

.refresh-btn:disabled {
    opacity: 0.6;
    cursor: not-allowed;
}

.refresh-btn.refreshing .btn-icon {
    animation: spin 1s linear infinite;
}

@keyframes spin {
    from { transform: rotate(0deg); }
    to { transform: rotate(360deg); }
}

/* 简单通知样式 */
.ui-toast {
    position: fixed;
    top: 20px;
    right: 20px;
    background: var(--bg-secondary);
    border: 2px solid var(--accent-primary);
    border-radius: 8px;
    padding: 1rem 1.5rem;
    color: var(--text-primary);
    font-size: 0.9rem;
    font-weight: bold;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.5);
    z-index: 10001;
    transform: translateX(400px);
    opacity: 0;
    transition: all 0.5s ease;
    max-width: 300px;
}

.ui-toast.show {
    transform: translateX(0);
    opacity: 1;
}

/* 响应式设计 */
@media (max-width: 768px) {
    .settings-panel .settings-container {
        width: 95%;
        height: 95%;
    }
    
    .settings-tabs {
        flex-wrap: wrap;
    }
    
    .settings-tab {
        flex: 1;
        min-width: 80px;
        padding: 10px;
        font-size: 14px;
    }
    
    .settings-item {
        flex-direction: column;
        align-items: flex-start;
        gap: 10px;
    }
    
    .slider-container {
        width: 100%;
    }
    
    .hud-score-container,
    .hud-energy-container,
    .hud-frequency-container,
    .hud-combo-container {
        position: relative;
        top: auto;
        left: auto;
        right: auto;
        bottom: auto;
        transform: none;
        margin-bottom: 10px;
    }
    
    #gameHUD {
        flex-direction: row;
        flex-wrap: wrap;
        justify-content: space-between;
    }

    /* 关卡选择响应式 */
    .level-select-content {
        flex-direction: column;
        gap: 15px;
    }

    .level-grid {
        grid-template-columns: 1fr;
        max-height: 50vh;
    }

    .level-preview {
        max-width: none;
    }

    .preview-stats {
        grid-template-columns: 1fr;
        gap: 10px;
    }

    .difficulty-buttons {
        flex-direction: column;
    }

    .difficulty-button {
        width: 100%;
    }
}

/* PC端大屏幕优化 */
@media (min-width: 1200px) {
    .level-select-content {
        max-width: 1400px;
        margin: 0 auto;
    }

    .level-preview {
        max-width: 450px; /* PC端增加预览区域宽度 */
        max-height: calc(100vh - 120px); /* PC端优化高度计算 */
    }

    .level-grid {
        max-height: calc(100vh - 120px); /* 确保网格区域也有合适的高度 */
    }

    /* PC端难度选择器优化 */
    .difficulty-buttons {
        gap: 15px; /* PC端增加按钮间距 */
    }

    .difficulty-button {
        padding: 10px 20px; /* PC端增加按钮内边距 */
        font-size: 15px; /* PC端稍微增大字体 */
    }
}

/* 超大屏幕优化 */
@media (min-width: 1600px) {
    .level-select-content {
        max-width: 1600px;
        gap: 30px; /* 超大屏幕增加间距 */
    }

    .level-preview {
        max-width: 500px;
        padding: 30px; /* 超大屏幕增加内边距 */
    }
}
