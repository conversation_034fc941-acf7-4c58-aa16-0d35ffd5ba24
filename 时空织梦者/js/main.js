/**
 * 时空织梦者 - 主程序入口
 * 游戏初始化和主循环控制
 * 
 * 功能特性:
 * - 游戏初始化流程
 * - 主游戏循环
 * - 错误处理和恢复
 * - 性能监控
 * - 全局事件管理
 */

class TemporalDreamWeaverGame {
    constructor() {
        // 游戏状态
        this.isInitialized = false;
        this.isRunning = false;
        this.isPaused = false;
        
        // 核心系统实例
        this.timeEngine = null;
        this.renderer = null;
        this.inputHandler = null;
        this.screenManager = null;
        this.gameState = null;
        
        // 性能监控
        this.frameCount = 0;
        this.fps = 0;
        this.lastFpsUpdate = 0;
        this.performanceStats = {
            avgFrameTime: 0,
            minFrameTime: Infinity,
            maxFrameTime: 0,
            totalFrames: 0
        };
        
        // 错误处理
        this.errorCount = 0;
        this.maxErrors = 10;
        
        console.log('🎮 时空织梦者游戏实例创建');
    }

    /**
     * 初始化游戏
     */
    async init() {
        try {
            console.log('🚀 开始初始化游戏...');
            
            // 显示加载屏幕
            this._showLoadingScreen();
            
            // 初始化核心服务
            await this._initializeCoreServices();
            
            // 初始化游戏系统
            await this._initializeGameSystems();
            
            // 设置事件监听器
            this._setupEventListeners();
            
            // 初始化完成
            this.isInitialized = true;
            console.log('✅ 游戏初始化完成');
            
            // 隐藏加载屏幕，显示主菜单
            this._hideLoadingScreen();
            this._showMainMenu();
            
        } catch (error) {
            console.error('❌ 游戏初始化失败:', error);
            this._handleInitializationError(error);
        }
    }

    /**
     * 显示加载屏幕
     */
    _showLoadingScreen() {
        const loadingScreen = document.getElementById('loading-screen');
        if (loadingScreen) {
            loadingScreen.classList.add('active');
            this._updateLoadingProgress(0, i18nService.t('loading.initializing'));
        }
    }

    /**
     * 更新加载进度
     * @param {number} progress - 进度 (0-100)
     * @param {string} message - 加载信息
     */
    _updateLoadingProgress(progress, message) {
        const progressFill = document.querySelector('.progress-fill');
        const loadingText = document.querySelector('.loading-text');
        
        if (progressFill) {
            progressFill.style.width = `${progress}%`;
        }
        
        if (loadingText) {
            loadingText.textContent = message;
        }
    }

    /**
     * 初始化核心服务
     */
    async _initializeCoreServices() {
        console.log('🔧 初始化核心服务...');
        
        // 初始化存储服务
        this._updateLoadingProgress(20, i18nService.t('loading.initializing'));
        await storageService.init();
        
        // 初始化国际化服务
        this._updateLoadingProgress(40, i18nService.t('loading.loading_assets'));
        await i18nService.init();
        
        console.log('✅ 核心服务初始化完成');
    }

    /**
     * 初始化游戏系统
     */
    async _initializeGameSystems() {
        console.log('🎯 初始化游戏系统...');
        
        // 初始化时间引擎
        this._updateLoadingProgress(60, i18nService.t('loading.preparing_game'));
        this.timeEngine = new TimeEngine();
        this.timeEngine.init();
        
        // 初始化屏幕管理器
        this._updateLoadingProgress(80, i18nService.t('loading.preparing_game'));
        if (typeof ScreenManager !== 'undefined') {
            this.screenManager = new ScreenManager();
            this.screenManager.init();
        }
        
        // 初始化输入处理器
        if (typeof InputHandler !== 'undefined') {
            this.inputHandler = new InputHandler();
            this.inputHandler.init();
        }
        
        // 初始化游戏状态管理器
        if (typeof GameState !== 'undefined') {
            this.gameState = new GameState();
            this.gameState.init();
        }
        
        this._updateLoadingProgress(100, i18nService.t('loading.complete'));
        
        console.log('✅ 游戏系统初始化完成');
    }

    /**
     * 设置事件监听器
     */
    _setupEventListeners() {
        console.log('📡 设置事件监听器...');
        
        // 窗口事件
        window.addEventListener('resize', this._handleResize.bind(this));
        window.addEventListener('beforeunload', this._handleBeforeUnload.bind(this));
        window.addEventListener('visibilitychange', this._handleVisibilityChange.bind(this));
        
        // 错误处理
        window.addEventListener('error', this._handleGlobalError.bind(this));
        window.addEventListener('unhandledrejection', this._handleUnhandledRejection.bind(this));
        
        // 键盘事件
        document.addEventListener('keydown', this._handleKeyDown.bind(this));
        document.addEventListener('keyup', this._handleKeyUp.bind(this));
        
        // 游戏特定事件
        if (this.timeEngine) {
            this.timeEngine.addEventListener('stateChanged', this._handleTimeStateChange.bind(this));
        }
        
        console.log('✅ 事件监听器设置完成');
    }

    /**
     * 隐藏加载屏幕
     */
    _hideLoadingScreen() {
        const loadingScreen = document.getElementById('loading-screen');
        if (loadingScreen) {
            setTimeout(() => {
                loadingScreen.classList.remove('active');
            }, 500);
        }
    }

    /**
     * 显示主菜单
     */
    _showMainMenu() {
        if (this.screenManager) {
            this.screenManager.showScreen('main-menu');
        } else {
            // 备用方案：直接显示主菜单
            const mainMenuScreen = document.getElementById('main-menu-screen');
            if (mainMenuScreen) {
                mainMenuScreen.classList.add('active');
            }
        }
    }

    /**
     * 启动游戏主循环
     */
    start() {
        if (!this.isInitialized) {
            console.warn('⚠️ 游戏未初始化，无法启动');
            return;
        }
        
        if (this.isRunning) {
            console.warn('⚠️ 游戏已在运行中');
            return;
        }
        
        this.isRunning = true;
        this.lastFpsUpdate = performance.now();
        
        console.log('▶️ 游戏主循环启动');
        this._gameLoop();
    }

    /**
     * 停止游戏
     */
    stop() {
        this.isRunning = false;
        console.log('⏹️ 游戏已停止');
    }

    /**
     * 暂停游戏
     */
    pause() {
        this.isPaused = true;
        if (this.timeEngine) {
            this.timeEngine.pauseTime();
        }
        console.log('⏸️ 游戏已暂停');
    }

    /**
     * 恢复游戏
     */
    resume() {
        this.isPaused = false;
        if (this.timeEngine) {
            this.timeEngine.resumeTime();
        }
        console.log('▶️ 游戏已恢复');
    }

    /**
     * 游戏主循环
     */
    _gameLoop() {
        if (!this.isRunning) return;
        
        const currentTime = performance.now();
        
        try {
            // 更新性能统计
            this._updatePerformanceStats(currentTime);
            
            // 更新游戏系统
            if (!this.isPaused) {
                this._updateGameSystems(currentTime);
            }
            
            // 渲染游戏
            this._render(currentTime);
            
        } catch (error) {
            this._handleGameLoopError(error);
        }
        
        // 请求下一帧
        requestAnimationFrame(() => this._gameLoop());
    }

    /**
     * 更新游戏系统
     * @param {number} currentTime - 当前时间
     */
    _updateGameSystems(currentTime) {
        // 更新时间引擎
        if (this.timeEngine) {
            this.timeEngine.update(currentTime);
        }
        
        // 更新输入处理器
        if (this.inputHandler) {
            this.inputHandler.update(currentTime);
        }
        
        // 更新游戏状态
        if (this.gameState) {
            this.gameState.update(currentTime);
        }
        
        // 更新屏幕管理器
        if (this.screenManager) {
            this.screenManager.update(currentTime);
        }
    }

    /**
     * 渲染游戏
     * @param {number} currentTime - 当前时间
     */
    _render(currentTime) {
        // 渲染当前屏幕
        if (this.screenManager) {
            this.screenManager.render(currentTime);
        }
        
        // 渲染调试信息
        if (this._isDebugMode()) {
            this._renderDebugInfo();
        }
    }

    /**
     * 更新性能统计
     * @param {number} currentTime - 当前时间
     */
    _updatePerformanceStats(currentTime) {
        this.frameCount++;
        this.performanceStats.totalFrames++;
        
        // 计算帧时间
        const frameTime = currentTime - (this.lastFrameTime || currentTime);
        this.lastFrameTime = currentTime;
        
        // 更新帧时间统计
        this.performanceStats.avgFrameTime = 
            (this.performanceStats.avgFrameTime * (this.frameCount - 1) + frameTime) / this.frameCount;
        this.performanceStats.minFrameTime = Math.min(this.performanceStats.minFrameTime, frameTime);
        this.performanceStats.maxFrameTime = Math.max(this.performanceStats.maxFrameTime, frameTime);
        
        // 每秒更新一次FPS
        if (currentTime - this.lastFpsUpdate >= 1000) {
            this.fps = this.frameCount;
            this.frameCount = 0;
            this.lastFpsUpdate = currentTime;
        }
    }

    /**
     * 处理窗口大小变化
     */
    _handleResize() {
        if (this.renderer) {
            this.renderer.handleResize();
        }
        
        console.log('📐 窗口大小已调整');
    }

    /**
     * 处理页面卸载前事件
     */
    _handleBeforeUnload(event) {
        // 保存游戏状态
        if (this.gameState) {
            this.gameState.saveState();
        }
        
        console.log('💾 游戏状态已保存');
    }

    /**
     * 处理页面可见性变化
     */
    _handleVisibilityChange() {
        if (document.hidden) {
            this.pause();
        } else {
            this.resume();
        }
    }

    /**
     * 处理键盘按下事件
     * @param {KeyboardEvent} event - 键盘事件
     */
    _handleKeyDown(event) {
        // 全局快捷键处理
        switch (event.code) {
            case 'F11':
                event.preventDefault();
                this._toggleFullscreen();
                break;
            case 'Escape':
                if (this.screenManager) {
                    this.screenManager.handleEscape();
                }
                break;
        }
    }

    /**
     * 处理键盘释放事件
     * @param {KeyboardEvent} event - 键盘事件
     */
    _handleKeyUp(event) {
        // 可以在这里处理键盘释放事件
    }

    /**
     * 处理时间状态变化
     * @param {object} data - 时间状态数据
     */
    _handleTimeStateChange(data) {
        console.log(`⏰ 时间状态变化: ${data.oldState} → ${data.newState}`);
    }

    /**
     * 切换全屏模式
     */
    _toggleFullscreen() {
        if (!document.fullscreenElement) {
            document.documentElement.requestFullscreen().catch(err => {
                console.warn('⚠️ 无法进入全屏模式:', err);
            });
        } else {
            document.exitFullscreen().catch(err => {
                console.warn('⚠️ 无法退出全屏模式:', err);
            });
        }
    }

    /**
     * 处理全局错误
     * @param {ErrorEvent} event - 错误事件
     */
    _handleGlobalError(event) {
        this.errorCount++;
        console.error('❌ 全局错误:', event.error);
        
        if (this.errorCount > this.maxErrors) {
            console.error('❌ 错误过多，停止游戏');
            this.stop();
        }
    }

    /**
     * 处理未捕获的Promise拒绝
     * @param {PromiseRejectionEvent} event - Promise拒绝事件
     */
    _handleUnhandledRejection(event) {
        console.error('❌ 未处理的Promise拒绝:', event.reason);
        event.preventDefault();
    }

    /**
     * 处理初始化错误
     * @param {Error} error - 错误对象
     */
    _handleInitializationError(error) {
        const loadingText = document.querySelector('.loading-text');
        if (loadingText) {
            loadingText.textContent = '初始化失败，请刷新页面重试';
            loadingText.style.color = '#ef4444';
        }
    }

    /**
     * 处理游戏循环错误
     * @param {Error} error - 错误对象
     */
    _handleGameLoopError(error) {
        this.errorCount++;
        console.error('❌ 游戏循环错误:', error);
        
        if (this.errorCount > this.maxErrors) {
            this.stop();
        }
    }

    /**
     * 检查是否为调试模式
     * @returns {boolean} 是否为调试模式
     */
    _isDebugMode() {
        return localStorage.getItem('debug') === 'true' || 
               window.location.search.includes('debug=true');
    }

    /**
     * 渲染调试信息
     */
    _renderDebugInfo() {
        // 在这里可以渲染FPS、性能统计等调试信息
        // 具体实现可以在后续添加
    }

    /**
     * 获取游戏统计信息
     * @returns {object} 统计信息
     */
    getStats() {
        return {
            fps: this.fps,
            frameCount: this.performanceStats.totalFrames,
            avgFrameTime: this.performanceStats.avgFrameTime,
            minFrameTime: this.performanceStats.minFrameTime,
            maxFrameTime: this.performanceStats.maxFrameTime,
            errorCount: this.errorCount,
            isRunning: this.isRunning,
            isPaused: this.isPaused,
            timeInfo: this.timeEngine ? this.timeEngine.getTimeInfo() : null
        };
    }
}

// 游戏启动
document.addEventListener('DOMContentLoaded', async () => {
    console.log('🌟 时空织梦者启动中...');
    
    // 创建游戏实例
    window.game = new TemporalDreamWeaverGame();
    
    // 初始化并启动游戏
    await window.game.init();
    window.game.start();
    
    console.log('🎮 时空织梦者已启动！');
});

// 导出游戏类
if (typeof module !== 'undefined' && module.exports) {
    module.exports = { TemporalDreamWeaverGame };
} else {
    window.TemporalDreamWeaverGame = TemporalDreamWeaverGame;
}
